{"type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "seed:products": "node scripts/seedProducts.js"}, "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^5.1.0", "express-session": "^1.18.1", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "multer": "^2.0.1", "nodemailer": "^7.0.3", "openai": "^5.8.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}, "name": "gold-backend", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}