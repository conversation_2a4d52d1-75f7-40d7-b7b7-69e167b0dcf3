<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Market Platform - Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .slide {
            min-height: 100vh;
            display: none;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.6s ease-in-out;
        }

        .slide.active {
            display: flex;
            opacity: 1;
            transform: translateX(0);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #C37C00 0%, #A66A00 50%, #8A5700 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card {
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(195, 124, 0, 0.3);
        }

        .tech-badge {
            background: linear-gradient(45deg, #C37C00, #A66A00);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 4px;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(195, 124, 0, 0.3);
        }

        .progress-bar {
            height: 4px;
            background: linear-gradient(90deg, #C37C00 0%, #A66A00 100%);
            transition: width 0.3s ease;
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .code-block {
            background: #1a1a1a;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .highlight {
            background: linear-gradient(120deg, rgba(195, 124, 0, 0.3) 0%, rgba(195, 124, 0, 0.1) 100%);
            padding: 2px 6px;
            border-radius: 4px;
        }
    </style>
</head>

<body class="bg-gray-900 text-white overflow-hidden">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 glass-effect p-4">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                    <i data-lucide="crown" class="w-6 h-6 text-white"></i>
                </div>
                <h1 class="text-xl font-bold">Gold Market Platform</h1>
            </div>

            <div class="flex items-center space-x-4">
                <span id="slideCounter" class="text-sm opacity-75">1 / 9</span>
                <div class="flex space-x-2">
                    <button onclick="previousSlide()" class="p-2 hover:bg-white/10 rounded-full transition-colors">
                        <i data-lucide="chevron-left" class="w-5 h-5"></i>
                    </button>
                    <button onclick="nextSlide()" class="p-2 hover:bg-white/10 rounded-full transition-colors">
                        <i data-lucide="chevron-right" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="w-full bg-gray-700 h-1 mt-4">
            <div id="progressBar" class="progress-bar w-1/9"></div>
        </div>
    </nav>

    <!-- Slide 1: Introduction -->
    <section class="slide active flex items-center justify-center gradient-bg">
        <div class="text-center max-w-4xl mx-auto px-8">
            <div class="animate-float mb-8">
                <div
                    class="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                    <i data-lucide="crown" class="w-16 h-16 text-white"></i>
                </div>
            </div>

            <h1 class="text-6xl font-bold mb-6 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                Gold Market Platform
            </h1>

            <p class="text-2xl mb-8 text-white/90 leading-relaxed">
                Revolutionizing Egypt's Gold Industry Through Digital Innovation
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                <div class="glass-effect p-6 rounded-2xl">
                    <i data-lucide="store" class="w-12 h-12 text-yellow-300 mx-auto mb-4"></i>
                    <h3 class="text-xl font-semibold mb-2">Multi-Shop Platform</h3>
                    <p class="text-white/80">Connecting verified gold shops nationwide</p>
                </div>

                <div class="glass-effect p-6 rounded-2xl">
                    <i data-lucide="bot" class="w-12 h-12 text-yellow-300 mx-auto mb-4"></i>
                    <h3 class="text-xl font-semibold mb-2">AI-Powered</h3>
                    <p class="text-white/80">Intelligent chatbot and automation</p>
                </div>

                <div class="glass-effect p-6 rounded-2xl">
                    <i data-lucide="smartphone" class="w-12 h-12 text-yellow-300 mx-auto mb-4"></i>
                    <h3 class="text-xl font-semibold mb-2">Mobile-First</h3>
                    <p class="text-white/80">Responsive design for all devices</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 2: Problem & Solution -->
    <section class="slide flex items-center justify-center bg-gray-900">
        <div class="max-w-7xl mx-auto px-8 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-5xl font-bold mb-8 text-red-400">The Problem</h2>

                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div
                            class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                            <i data-lucide="x" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Limited Online Presence</h3>
                            <p class="text-gray-400">Traditional gold shops struggle with digital visibility</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div
                            class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                            <i data-lucide="x" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Trust Issues</h3>
                            <p class="text-gray-400">Customers can't verify shop authenticity online</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div
                            class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                            <i data-lucide="x" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Manual Processes</h3>
                            <p class="text-gray-400">Inefficient appointment booking and management</p>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h2 class="text-5xl font-bold mb-8 text-green-400">Our Solution</h2>

                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div
                            class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Centralized Platform</h3>
                            <p class="text-gray-400">All verified shops in one digital marketplace</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div
                            class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Trust System</h3>
                            <p class="text-gray-400">Verification badges and customer reviews</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div
                            class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Smart Automation</h3>
                            <p class="text-gray-400">AI-powered booking and customer support</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8 p-6 gradient-bg rounded-2xl">
                    <h3 class="text-2xl font-bold mb-4">Impact Metrics</h3>
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-3xl font-bold text-yellow-300">300%</div>
                            <div class="text-sm text-white/80">Visibility Increase</div>
                        </div>
                        <div>
                            <div class="text-3xl font-bold text-yellow-300">95%</div>
                            <div class="text-sm text-white/80">Customer Satisfaction</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 3: Core Features -->
    <section class="slide flex items-center justify-center bg-gray-800">
        <div class="max-w-7xl mx-auto px-8">
            <h2
                class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Core Features
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- User Management -->
                <div class="feature-card bg-gray-700 p-8 rounded-2xl">
                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mb-6">
                        <i data-lucide="users" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">User Management</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Multi-role authentication</li>
                        <li>• Google OAuth integration</li>
                        <li>• Profile management</li>
                        <li>• Favorites system</li>
                    </ul>
                </div>

                <!-- Shop Management -->
                <div class="feature-card bg-gray-700 p-8 rounded-2xl">
                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mb-6">
                        <i data-lucide="store" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Shop Management</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Complete business profiles</li>
                        <li>• Image galleries</li>
                        <li>• Verification system</li>
                        <li>• Analytics dashboard</li>
                    </ul>
                </div>

                <!-- Booking System -->
                <div class="feature-card bg-gray-700 p-8 rounded-2xl">
                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mb-6">
                        <i data-lucide="calendar" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Smart Booking</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Real-time availability</li>
                        <li>• Flexible scheduling</li>
                        <li>• Instant confirmations</li>
                        <li>• Cancellation management</li>
                    </ul>
                </div>

                <!-- Product Catalog -->
                <div class="feature-card bg-gray-700 p-8 rounded-2xl">
                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mb-6">
                        <i data-lucide="gem" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Product Catalog</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Rich product data</li>
                        <li>• Multiple images</li>
                        <li>• Gold specifications</li>
                        <li>• AI descriptions</li>
                    </ul>
                </div>

                <!-- Rating System -->
                <div class="feature-card bg-gray-700 p-8 rounded-2xl">
                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mb-6">
                        <i data-lucide="star" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Rating & Reviews</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• 5-star rating system</li>
                        <li>• Written reviews</li>
                        <li>• Photo testimonials</li>
                        <li>• Trust building</li>
                    </ul>
                </div>

                <!-- AI Integration -->
                <div class="feature-card bg-gray-700 p-8 rounded-2xl">
                    <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mb-6">
                        <i data-lucide="bot" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">AI Assistant</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li>• Intelligent chatbot</li>
                        <li>• Auto descriptions</li>
                        <li>• 24/7 support</li>
                        <li>• Natural language</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 4: System Architecture -->
    <section class="slide flex items-center justify-center bg-gray-900">
        <div class="max-w-7xl mx-auto px-8">
            <h2
                class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                System Architecture
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Frontend Architecture -->
                <div class="bg-gray-800 p-8 rounded-2xl">
                    <h3 class="text-3xl font-bold mb-6 text-blue-400">Frontend Architecture</h3>
                    <div class="space-y-4">
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-lg mb-2">React 18 Application</h4>
                            <div class="ml-4 space-y-2 text-gray-300">
                                <div>├── Components Layer</div>
                                <div>├── Pages Layer</div>
                                <div>├── Services Layer</div>
                                <div>└── State Management</div>
                            </div>
                        </div>

                        <div class="bg-blue-900/30 p-4 rounded-lg">
                            <h4 class="font-semibold text-lg mb-2">Key Technologies</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="tech-badge">React 18</span>
                                <span class="tech-badge">Vite</span>
                                <span class="tech-badge">Tailwind CSS</span>
                                <span class="tech-badge">shadcn/ui</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Backend Architecture -->
                <div class="bg-gray-800 p-8 rounded-2xl">
                    <h3 class="text-3xl font-bold mb-6 text-purple-400">Backend Architecture</h3>
                    <div class="space-y-4">
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-lg mb-2">Node.js/Express Server</h4>
                            <div class="ml-4 space-y-2 text-gray-300">
                                <div>├── Controllers Layer</div>
                                <div>├── Services Layer</div>
                                <div>├── Models Layer</div>
                                <div>└── Middleware Layer</div>
                            </div>
                        </div>

                        <div class="bg-purple-900/30 p-4 rounded-lg">
                            <h4 class="font-semibold text-lg mb-2">Key Technologies</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="tech-badge">Node.js</span>
                                <span class="tech-badge">Express</span>
                                <span class="tech-badge">MongoDB</span>
                                <span class="tech-badge">OpenAI</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Schema -->
            <div class="mt-12 bg-gray-800 p-8 rounded-2xl">
                <h3 class="text-3xl font-bold mb-6 text-green-400">Database Schema</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h4 class="font-semibold text-lg mb-3 text-green-300">Core Collections</h4>
                        <ul class="space-y-1 text-gray-300 text-sm">
                            <li>• Users (Authentication)</li>
                            <li>• Shops (Business Data)</li>
                            <li>• Products (Catalog)</li>
                        </ul>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h4 class="font-semibold text-lg mb-3 text-green-300">Booking System</h4>
                        <ul class="space-y-1 text-gray-300 text-sm">
                            <li>• BookingTimes</li>
                            <li>• Appointments</li>
                            <li>• Availability</li>
                        </ul>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <h4 class="font-semibold text-lg mb-3 text-green-300">Social Features</h4>
                        <ul class="space-y-1 text-gray-300 text-sm">
                            <li>• ShopRatings</li>
                            <li>• Conversations</li>
                            <li>• Favorites</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 5: AI Integration -->
    <section class="slide flex items-center justify-center bg-gradient-to-br from-purple-900 to-blue-900">
        <div class="max-w-7xl mx-auto px-8">
            <h2
                class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                AI Integration
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h3 class="text-3xl font-bold mb-6 text-purple-300">OpenAI GPT-4 Integration</h3>

                    <div class="space-y-6">
                        <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                            <h4 class="text-xl font-semibold mb-3 text-purple-200">Intelligent Chatbot</h4>
                            <ul class="space-y-2 text-gray-300">
                                <li>• Natural Language Processing</li>
                                <li>• Context-Aware Responses</li>
                                <li>• Arabic & English Support</li>
                                <li>• 24/7 Customer Support</li>
                            </ul>
                        </div>

                        <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                            <h4 class="text-xl font-semibold mb-3 text-purple-200">Auto Content Generation</h4>
                            <ul class="space-y-2 text-gray-300">
                                <li>• Product Descriptions</li>
                                <li>• SEO Optimization</li>
                                <li>• Multi-Language Content</li>
                                <li>• Variation Generation</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-2xl font-bold mb-4 text-purple-300">Code Implementation</h3>
                    <div class="code-block">
                        <pre><code class="text-sm">const getChatbotResponse = async (message, user) => {
  // Extract keywords and context
  const keywords = message.split(/\s+/).filter(Boolean);

  // Query relevant data
  const products = await Product.find({
    $or: [
      { title: { $regex: keywords, $options: "i" } },
      { description: { $regex: keywords, $options: "i" } }
    ]
  }).populate('shop', 'name');

  // Build context for AI
  const context = products.map(p =>
    `${p.title} - ${p.description}`
  ).join('; ');

  // Generate AI response
  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "Assistant for gold platform" },
      { role: "user", content: `Context: ${context}\nUser: ${message}` }
    ],
    max_tokens: 300,
    temperature: 0.2
  });

  return response.choices[0].message.content;
};</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 6: Tech Stack -->
    <section class="slide flex items-center justify-center bg-gray-800">
        <div class="max-w-7xl mx-auto px-8">
            <h2
                class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
                Technology Stack
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Frontend -->
                <div class="bg-gray-700 p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="monitor" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-blue-400">Frontend</h3>
                    <div class="space-y-2">
                        <span class="tech-badge">React 18</span>
                        <span class="tech-badge">Vite</span>
                        <span class="tech-badge">Tailwind CSS</span>
                        <span class="tech-badge">shadcn/ui</span>
                        <span class="tech-badge">Framer Motion</span>
                        <span class="tech-badge">Lucide React</span>
                    </div>
                </div>

                <!-- Backend -->
                <div class="bg-gray-700 p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="server" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-green-400">Backend</h3>
                    <div class="space-y-2">
                        <span class="tech-badge">Node.js</span>
                        <span class="tech-badge">Express.js</span>
                        <span class="tech-badge">MongoDB</span>
                        <span class="tech-badge">Mongoose</span>
                        <span class="tech-badge">JWT</span>
                        <span class="tech-badge">bcrypt</span>
                    </div>
                </div>

                <!-- AI & Services -->
                <div class="bg-gray-700 p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="brain" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-purple-400">AI & Services</h3>
                    <div class="space-y-2">
                        <span class="tech-badge">OpenAI GPT-4</span>
                        <span class="tech-badge">Google OAuth</span>
                        <span class="tech-badge">Multer</span>
                        <span class="tech-badge">Nodemailer</span>
                        <span class="tech-badge">Socket.IO</span>
                        <span class="tech-badge">Passport.js</span>
                    </div>
                </div>

                <!-- DevOps -->
                <div class="bg-gray-700 p-8 rounded-2xl text-center">
                    <div class="w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="settings" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-orange-400">DevOps</h3>
                    <div class="space-y-2">
                        <span class="tech-badge">Git</span>
                        <span class="tech-badge">npm/pnpm</span>
                        <span class="tech-badge">ESLint</span>
                        <span class="tech-badge">Prettier</span>
                        <span class="tech-badge">dotenv</span>
                        <span class="tech-badge">CORS</span>
                    </div>
                </div>
            </div>

            <!-- Architecture Diagram -->
            <div class="mt-12 bg-gray-700 p-8 rounded-2xl">
                <h3 class="text-3xl font-bold text-center mb-8 text-yellow-400">System Flow</h3>
                <div class="flex items-center justify-center space-x-8">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mb-4">
                            <i data-lucide="smartphone" class="w-10 h-10 text-white"></i>
                        </div>
                        <p class="text-blue-400 font-semibold">React Frontend</p>
                    </div>

                    <i data-lucide="arrow-right" class="w-8 h-8 text-gray-400"></i>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mb-4">
                            <i data-lucide="server" class="w-10 h-10 text-white"></i>
                        </div>
                        <p class="text-green-400 font-semibold">Express API</p>
                    </div>

                    <i data-lucide="arrow-right" class="w-8 h-8 text-gray-400"></i>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-purple-500 rounded-full flex items-center justify-center mb-4">
                            <i data-lucide="database" class="w-10 h-10 text-white"></i>
                        </div>
                        <p class="text-purple-400 font-semibold">MongoDB</p>
                    </div>

                    <i data-lucide="arrow-right" class="w-8 h-8 text-gray-400"></i>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-pink-500 rounded-full flex items-center justify-center mb-4">
                            <i data-lucide="bot" class="w-10 h-10 text-white"></i>
                        </div>
                        <p class="text-pink-400 font-semibold">OpenAI</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 7: Live Demo / Screenshots -->
    <section class="slide flex items-center justify-center bg-gradient-to-br from-indigo-900 to-purple-900">
        <div class="max-w-7xl mx-auto px-8">
            <h2
                class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                Live Demo & Features
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Demo Features -->
                <div class="space-y-8">
                    <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                        <h3 class="text-2xl font-bold mb-4 text-indigo-300">🏠 Homepage Features</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li>• Dynamic shop slider with golden theme</li>
                            <li>• Search functionality for shops and products</li>
                            <li>• Category navigation for jewelry types</li>
                            <li>• Trust indicators and verification badges</li>
                        </ul>
                    </div>

                    <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                        <h3 class="text-2xl font-bold mb-4 text-purple-300">🏪 Shop Details Page</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li>• Full-width image slider without gaps</li>
                            <li>• Comprehensive business information</li>
                            <li>• Product showcase with enhanced cards</li>
                            <li>• Booking interface with real-time slots</li>
                        </ul>
                    </div>

                    <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                        <h3 class="text-2xl font-bold mb-4 text-pink-300">🤖 AI Chatbot</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li>• Floating chat button on homepage</li>
                            <li>• Natural language understanding</li>
                            <li>• Context-aware product recommendations</li>
                            <li>• Bilingual support (Arabic/English)</li>
                        </ul>
                    </div>
                </div>

                <!-- Live Demo Access -->
                <div class="space-y-8">
                    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-8 rounded-2xl text-center">
                        <h3 class="text-3xl font-bold mb-6 text-white">🚀 Live Demo Access</h3>
                        <div class="space-y-4">
                            <div class="bg-white/20 p-4 rounded-lg">
                                <h4 class="font-semibold text-lg mb-2">Frontend Application</h4>
                                <code class="text-yellow-300">http://localhost:5173</code>
                            </div>
                            <div class="bg-white/20 p-4 rounded-lg">
                                <h4 class="font-semibold text-lg mb-2">Backend API</h4>
                                <code class="text-yellow-300">http://localhost:5002</code>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                        <h3 class="text-2xl font-bold mb-4 text-indigo-300">📱 Mobile Experience</h3>
                        <ul class="space-y-2 text-gray-300">
                            <li>• Fully responsive design</li>
                            <li>• Touch-optimized interface</li>
                            <li>• Fast loading performance</li>
                            <li>• Intuitive mobile navigation</li>
                        </ul>
                    </div>

                    <div class="bg-white/10 p-6 rounded-2xl backdrop-blur-sm">
                        <h3 class="text-2xl font-bold mb-4 text-purple-300">🔐 User Roles</h3>
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div
                                    class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="user" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="text-sm">Customer</p>
                            </div>
                            <div>
                                <div
                                    class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="store" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="text-sm">Shop Owner</p>
                            </div>
                            <div>
                                <div
                                    class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <i data-lucide="shield" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="text-sm">Admin</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 8: Future Roadmap -->
    <section class="slide flex items-center justify-center bg-gray-900">
        <div class="max-w-7xl mx-auto px-8">
            <h2
                class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-yellow-400 to-red-500 bg-clip-text text-transparent">
                Future Roadmap
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Phase 1 -->
                <div class="bg-gradient-to-br from-blue-600 to-blue-800 p-8 rounded-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">Q2</span>
                        </div>
                        <h3 class="text-2xl font-bold text-white">Enhanced Features</h3>
                    </div>
                    <ul class="space-y-3 text-blue-100">
                        <li>• AI-Powered Recommendations</li>
                        <li>• Visual Search</li>
                        <li>• Payment Integration</li>
                        <li>• Advanced Filters</li>
                        <li>• Wishlist Sharing</li>
                    </ul>
                </div>

                <!-- Phase 2 -->
                <div class="bg-gradient-to-br from-green-600 to-green-800 p-8 rounded-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">Q3</span>
                        </div>
                        <h3 class="text-2xl font-bold text-white">Mobile Apps</h3>
                    </div>
                    <ul class="space-y-3 text-green-100">
                        <li>• Native iOS App</li>
                        <li>• Native Android App</li>
                        <li>• Push Notifications</li>
                        <li>• GPS Integration</li>
                        <li>• Offline Mode</li>
                    </ul>
                </div>

                <!-- Phase 3 -->
                <div class="bg-gradient-to-br from-purple-600 to-purple-800 p-8 rounded-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">Q4</span>
                        </div>
                        <h3 class="text-2xl font-bold text-white">Advanced Analytics</h3>
                    </div>
                    <ul class="space-y-3 text-purple-100">
                        <li>• Business Intelligence</li>
                        <li>• Predictive Analytics</li>
                        <li>• Voice Assistant</li>
                        <li>• Image Recognition</li>
                        <li>• Market Trends</li>
                    </ul>
                </div>

                <!-- Phase 4 -->
                <div class="bg-gradient-to-br from-orange-600 to-red-600 p-8 rounded-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">Q1</span>
                        </div>
                        <h3 class="text-2xl font-bold text-white">Global Expansion</h3>
                    </div>
                    <ul class="space-y-3 text-orange-100">
                        <li>• Multi-Vendor Platform</li>
                        <li>• International Markets</li>
                        <li>• Multi-Language</li>
                        <li>• Multi-Currency</li>
                        <li>• Global Shipping</li>
                    </ul>
                </div>
            </div>

            <!-- Long-term Vision -->
            <div class="mt-12 bg-gradient-to-r from-yellow-600 to-orange-600 p-8 rounded-2xl">
                <h3 class="text-3xl font-bold text-center mb-8 text-white">Long-term Vision (2025+)</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
                    <div class="bg-white/20 p-4 rounded-lg">
                        <i data-lucide="eye" class="w-8 h-8 text-white mx-auto mb-2"></i>
                        <h4 class="font-semibold text-white mb-2">Augmented Reality</h4>
                        <p class="text-yellow-100 text-sm">Virtual try-on experiences</p>
                    </div>
                    <div class="bg-white/20 p-4 rounded-lg">
                        <i data-lucide="link" class="w-8 h-8 text-white mx-auto mb-2"></i>
                        <h4 class="font-semibold text-white mb-2">Blockchain</h4>
                        <p class="text-yellow-100 text-sm">Authenticity verification</p>
                    </div>
                    <div class="bg-white/20 p-4 rounded-lg">
                        <i data-lucide="radio" class="w-8 h-8 text-white mx-auto mb-2"></i>
                        <h4 class="font-semibold text-white mb-2">IoT Integration</h4>
                        <p class="text-yellow-100 text-sm">Smart jewelry tracking</p>
                    </div>
                    <div class="bg-white/20 p-4 rounded-lg">
                        <i data-lucide="globe" class="w-8 h-8 text-white mx-auto mb-2"></i>
                        <h4 class="font-semibold text-white mb-2">Market Expansion</h4>
                        <p class="text-yellow-100 text-sm">Other precious metals</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Slide 9: Conclusion -->
    <section class="slide flex items-center justify-center gradient-bg">
        <div class="max-w-6xl mx-auto px-8 text-center">
            <h2 class="text-6xl font-bold mb-8 text-white">
                Thank You!
            </h2>

            <p class="text-2xl mb-12 text-white/90 leading-relaxed max-w-4xl mx-auto">
                Gold Market Platform represents the future of jewelry commerce in Egypt, combining traditional
                craftsmanship with modern technology to create an unparalleled shopping experience.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <!-- Technical Excellence -->
                <div class="glass-effect p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="code" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Technical Excellence</h3>
                    <ul class="space-y-2 text-white/80">
                        <li>✅ Full-Stack MERN Implementation</li>
                        <li>✅ AI Integration with OpenAI GPT-4</li>
                        <li>✅ Modern, Responsive Design</li>
                        <li>✅ Secure Authentication System</li>
                    </ul>
                </div>

                <!-- Business Impact -->
                <div class="glass-effect p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="trending-up" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Business Impact</h3>
                    <ul class="space-y-2 text-white/80">
                        <li>✅ First Digital Gold Marketplace</li>
                        <li>✅ Empowers Small Businesses</li>
                        <li>✅ Scalable Revenue Model</li>
                        <li>✅ National Growth Potential</li>
                    </ul>
                </div>

                <!-- Innovation -->
                <div class="glass-effect p-8 rounded-2xl">
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="lightbulb" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Innovation</h3>
                    <ul class="space-y-2 text-white/80">
                        <li>✅ AI-Powered Intelligence</li>
                        <li>✅ Trust & Verification System</li>
                        <li>✅ Mobile-First Approach</li>
                        <li>✅ Future-Ready Architecture</li>
                    </ul>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="glass-effect p-8 rounded-2xl">
                <h3 class="text-3xl font-bold mb-6 text-white">Ready for Demo & Questions</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-xl font-semibold mb-3 text-yellow-300">Live Demo</h4>
                        <p class="text-white/80">Experience the full platform functionality</p>
                        <code class="text-yellow-300 text-sm">localhost:5173</code>
                    </div>
                    <div>
                        <h4 class="text-xl font-semibold mb-3 text-yellow-300">Technical Details</h4>
                        <p class="text-white/80">Complete documentation and source code</p>
                        <code class="text-yellow-300 text-sm">GitHub Repository</code>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript for slide navigation -->
    <script>
        let currentSlide = 0;
        const totalSlides = 9;

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');
            const progressBar = document.getElementById('progressBar');
            const slideCounter = document.getElementById('slideCounter');

            slides.forEach(slide => slide.classList.remove('active'));

            if (n >= totalSlides) currentSlide = 0;
            if (n < 0) currentSlide = totalSlides - 1;

            slides[currentSlide].classList.add('active');

            // Update progress bar
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            progressBar.style.width = progress + '%';

            // Update slide counter
            slideCounter.textContent = `${currentSlide + 1} / ${totalSlides}`;
        }

        function nextSlide() {
            currentSlide++;
            showSlide(currentSlide);
        }

        function previousSlide() {
            currentSlide--;
            showSlide(currentSlide);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // Initialize Lucide icons
        lucide.createIcons();

        // Auto-advance slides (optional)
        // setInterval(nextSlide, 10000);
    </script>
</body>

</html>