import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button.jsx';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Badge } from '@/components/ui/badge.jsx';
import { useAuth } from '../../context/AuthContext.jsx';
import {
    ArrowLeft,
    Star,
    MapPin,
    Calendar,
    Heart,
    Eye,
    Grid,
    List,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import { shopService } from '../../services/shopService.js';
import { productService } from '../../services/productService.js';
import { rateService } from '../../services/rateService.js';
import { ROUTES } from '../../utils/constants.js';

// WhatsApp Icon Component
const WhatsAppIcon = () => (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
    </svg>
);

// Shop Image Slider Component
const ShopImageSlider = ({ images = [], shopName, defaultImage }) => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(true);

    // Filter and prepare images with better error handling
    const validImages = React.useMemo(() => {
        try {
            const filtered = images.filter(img => {
                if (!img) return false;
                if (typeof img === 'string') return img.trim() !== '';
                return true;
            });
            return filtered.length > 0 ? filtered : [defaultImage];
        } catch (error) {
            console.error('Error filtering images:', error);
            return [defaultImage];
        }
    }, [images, defaultImage]);

    const slideImages = validImages;

    // Define navigation functions first
    const goToSlide = React.useCallback((index) => {
        setCurrentSlide(index);
        setIsAutoPlaying(false);
        setTimeout(() => setIsAutoPlaying(true), 10000);
    }, []);

    const goToPrevious = React.useCallback(() => {
        setCurrentSlide((prev) => (prev - 1 + slideImages.length) % slideImages.length);
        setIsAutoPlaying(false);
        setTimeout(() => setIsAutoPlaying(true), 10000);
    }, [slideImages.length]);

    const goToNext = React.useCallback(() => {
        setCurrentSlide((prev) => (prev + 1) % slideImages.length);
        setIsAutoPlaying(false);
        setTimeout(() => setIsAutoPlaying(true), 10000);
    }, [slideImages.length]);

    // Auto slide functionality with pause on hover
    useEffect(() => {
        if (!isAutoPlaying || slideImages.length <= 1) return;

        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % slideImages.length);
        }, 6000);

        return () => clearInterval(interval);
    }, [isAutoPlaying, slideImages.length]);

    // Pause auto-play on hover
    const handleMouseEnter = () => setIsAutoPlaying(false);
    const handleMouseLeave = () => setIsAutoPlaying(true);

    // Keyboard navigation
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'ArrowLeft') {
                goToPrevious();
            } else if (event.key === 'ArrowRight') {
                goToNext();
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [goToPrevious, goToNext]);

    return (
        <div
            className="relative w-full h-full overflow-hidden group cursor-pointer"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
        >
            {/* Images */}
            <div
                className="flex transition-transform duration-1000 ease-in-out h-full"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
                {slideImages.map((image, index) => (
                    <div key={index} className="w-full h-full flex-shrink-0 relative overflow-hidden">
                        <img
                            src={image}
                            alt={`${shopName} - Image ${index + 1}`}
                            className="w-full h-full object-cover object-center transform transition-all duration-1000 group-hover:scale-110"
                            style={{
                                filter: 'brightness(1.2) contrast(1.25) saturate(1.4) sharpen(1.2)',
                                imageRendering: 'high-quality',
                                objectFit: 'cover',
                                objectPosition: 'center',
                                minHeight: '100%',
                                minWidth: '100%'
                            }}
                            loading={index === 0 ? "eager" : "lazy"}
                            onError={(e) => {
                                console.log('Image failed to load:', image);
                                if (e.target.src !== defaultImage) {
                                    e.target.src = defaultImage;
                                }
                            }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>
                ))}
            </div>

            {/* Navigation Arrows */}
            {slideImages.length > 1 && (
                <>
                    <button
                        onClick={goToPrevious}
                        className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white p-4 rounded-full transition-all duration-300 backdrop-blur-md shadow-2xl hover:scale-110 z-10"
                        aria-label="Previous image"
                    >
                        <ChevronLeft className="w-8 h-8" />
                    </button>
                    <button
                        onClick={goToNext}
                        className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white p-4 rounded-full transition-all duration-300 backdrop-blur-md shadow-2xl hover:scale-110 z-10"
                        aria-label="Next image"
                    >
                        <ChevronRight className="w-8 h-8" />
                    </button>
                </>
            )}

            {/* Dots Indicator */}
            {slideImages.length > 1 && (
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-4 z-10">
                    {slideImages.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={`w-4 h-4 rounded-full transition-all duration-300 backdrop-blur-md shadow-lg ${index === currentSlide
                                ? 'bg-[#C37C00] scale-150 shadow-2xl ring-2 ring-white/50'
                                : 'bg-white/80 hover:bg-white hover:scale-125 hover:shadow-xl'
                                }`}
                            aria-label={`Go to slide ${index + 1}`}
                        />
                    ))}
                </div>
            )}

            {/* Image Counter */}
            {slideImages.length > 1 && (
                <div className="absolute top-8 left-8 bg-black/70 text-white px-4 py-2 rounded-full text-base font-bold backdrop-blur-md shadow-xl z-10">
                    {currentSlide + 1} / {slideImages.length}
                </div>
            )}
        </div>
    );
};

const ShopDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { user } = useAuth();
    const [shop, setShop] = useState(null);
    const [products, setProducts] = useState([]);
    const [reviews, setReviews] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [viewMode, setViewMode] = useState('grid');

    const safeProducts = Array.isArray(products) ? products : [];
    const safeReviews = Array.isArray(reviews) ? reviews : [];

    useEffect(() => {
        if (id) {
            loadShopDetails();
        }
    }, [id]);

    const loadShopDetails = async () => {
        try {
            setIsLoading(true);
            console.log('🏪 Starting to load shop details for ID:', id);
            console.log('🏪 Current user:', user ? `${user.role} (${user.id})` : 'Not authenticated');

            try {
                let shopResponse;
                let shopData;
                let loadedSuccessfully = false;

                if (user) {
                    try {
                        console.log('🏪 Trying authenticated shop endpoint for shop:', id);
                        shopResponse = await shopService.getShopById(id);
                        shopData = shopResponse.data || shopResponse;
                        console.log('🏪 Authenticated shop data loaded:', shopData);
                        loadedSuccessfully = true;
                    } catch (authError) {
                        console.log('🏪 Authenticated endpoint failed:', authError.message);
                    }
                }

                if (!loadedSuccessfully) {
                    try {
                        console.log('🏪 Trying public shop endpoint for shop:', id);
                        shopResponse = await shopService.getPublicShop(id);
                        shopData = shopResponse.data || shopResponse;
                        console.log('🏪 Public shop data loaded:', shopData);
                        loadedSuccessfully = true;
                    } catch (publicError) {
                        console.log('🏪 Public endpoint failed:', publicError.message);
                    }
                }

                if (!loadedSuccessfully) {
                    throw new Error('Failed to load shop from both endpoints');
                }

                if (shopData && (shopData.id || shopData._id)) {
                    setShop(shopData);
                    console.log('🏪 Shop set successfully:', shopData.name || shopData.title || 'Unnamed Shop');
                    console.log('🏪 Shop data structure:', Object.keys(shopData));
                } else {
                    console.error('🏪 Invalid shop data received:', shopData);
                    throw new Error('Invalid shop data structure');
                }
            } catch (shopError) {
                console.error('🏪 Error loading shop:', shopError);
                console.error('🏪 Error details:', {
                    message: shopError.message,
                    response: shopError.response?.data,
                    status: shopError.response?.status
                });
                setShop(null);
                return;
            }

            const loadProducts = async () => {
                try {
                    console.log('📦 Loading products for shop:', id);
                    const productsResponse = await productService.getProductsByShop(id);
                    const productsData = Array.isArray(productsResponse)
                        ? productsResponse
                        : productsResponse.data || productsResponse.products || [];
                    console.log('📦 Sample product data:', productsData[0]);
                    console.log('📦 Product keys:', productsData[0] ? Object.keys(productsData[0]) : []);
                    setProducts(productsData);
                } catch (error) {
                    console.error('📦 Error loading products:', error);
                    setProducts([]);
                }
            };

            const loadReviews = async () => {
                try {
                    console.log('🌟 Loading reviews for shop:', id);
                    const reviewsResponse = await rateService.getAllRates({ shopId: id });
                    const reviewsData = Array.isArray(reviewsResponse)
                        ? reviewsResponse
                        : reviewsResponse.data || reviewsResponse.reviews || [];
                    console.log('🌟 Processed reviews data:', reviewsData);
                    setReviews(reviewsData);
                } catch (error) {
                    console.error('❌ Error loading reviews:', error);
                    setReviews([]);
                }
            };

            await Promise.allSettled([loadProducts(), loadReviews()]);
        } catch (error) {
            console.error('Error loading shop details:', error);
            setShop(null);
        } finally {
            setIsLoading(false);
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] to-[#F0E8DB] flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-8">
                    <div className="w-20 h-20 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Loading Shop Details</h3>
                    <p className="text-gray-600 mb-4">Please wait while we fetch the shop information...</p>
                    <div className="bg-white/50 rounded-xl p-4">
                        <p className="text-sm text-gray-500">Shop ID: {id}</p>
                    </div>
                </div>
            </div>
        );
    }

    if (!shop) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] to-[#F0E8DB] flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-8">
                    <div className="w-24 h-24 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                        <span className="text-4xl">🏪</span>
                    </div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop Not Available</h2>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                        This shop is currently unavailable. It might be pending approval, temporarily closed, or the shop ID might be incorrect.
                    </p>
                    <div className="space-y-3">
                        <Button
                            onClick={() => navigate(ROUTES.SHOPS)}
                            className="w-full bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to All Shops
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => window.location.reload()}
                            className="w-full border-2 border-[#C37C00] text-[#C37C00] hover:bg-[#C37C00] hover:text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300"
                        >
                            Try Again
                        </Button>
                    </div>
                    <p className="text-sm text-gray-500 mt-4">
                        Shop ID: {id}
                    </p>
                </div>
            </div>
        );
    }

    const defaultShopImage = 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop&crop=center&auto=format&q=60';

    let latitude = null;
    let longitude = null;
    if (shop.location && shop.location.coordinates) {
        longitude = shop.location.coordinates[0];
        latitude = shop.location.coordinates[1];
    }

    const safeShop = {
        id: shop.id || shop._id,
        name: shop.name || shop.title || 'Unnamed Shop',
        description: shop.description || 'No description available',
        address: shop.address || 'Address not specified',
        phone: shop.phone || 'Not specified',
        whatsapp: shop.whatsapp || null,
        rating: typeof shop.rating === 'number' ? shop.rating : 0,
        workingHours: shop.workingHours || 'Not specified',
        gallery: Array.isArray(shop.gallery) ? shop.gallery : [],
        images: Array.isArray(shop.images) ? shop.images : [],
        logoUrl: shop.logoUrl || shop.logo || null,
        image: shop.image || defaultShopImage,
        latitude: latitude,
        longitude: longitude,
        ownerId: shop.ownerId || shop.owner?.id || shop.owner,
        verified: shop.verified || false,
        status: shop.status || 'active',
        ownerName: shop.ownerName || 'Fady', // Assuming owner name is provided or default to 'Fady'
        ...shop
    };

    console.log('🏪 Safe shop data:', safeShop);

    return (
        <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB]">
            {/* Back to Shops Button Above Slider */}
            <div className="px-4 sm:px-6 lg:px-8 pt-6">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(-1)}
                    className="mb-6 bg-white/20 hover:bg-white/30 text-gray-900 border border-[#C37C00]/30 backdrop-blur-md transition-all duration-300 rounded-full px-6 py-2"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Shops
                </Button>
            </div>

            {/* Image Slider */}
            <div className="relative h-96 md:h-[600px] lg:h-[700px] overflow-hidden">
                <ShopImageSlider
                    images={(() => {
                        const shopImages = [];
                        console.log('🖼️ Processing shop images:', {
                            logoUrl: safeShop.logoUrl,
                            gallery: safeShop.gallery,
                            images: safeShop.images
                        });
                        if (safeShop.logoUrl) {
                            const logoImage = `${import.meta.env.VITE_API_BASE_URL}/shop-image/${safeShop.logoUrl}`;
                            shopImages.push(logoImage);
                            console.log('🖼️ Added logo image:', logoImage);
                        }
                        if (Array.isArray(safeShop.gallery)) {
                            safeShop.gallery.forEach((img, index) => {
                                if (img && typeof img === 'string' && img.trim() !== '') {
                                    shopImages.push(img);
                                    console.log(`🖼️ Added gallery image ${index}:`, img);
                                }
                            });
                        }
                        if (Array.isArray(safeShop.images)) {
                            safeShop.images.forEach((img, index) => {
                                if (img && typeof img === 'string' && img.trim() !== '') {
                                    shopImages.push(img);
                                    console.log(`🖼️ Added additional image ${index}:`, img);
                                }
                            });
                        }
                        console.log('🖼️ Final shop images array:', shopImages);
                        return shopImages.length > 0 ? shopImages : [defaultShopImage];
                    })()}
                    shopName={safeShop.name}
                    defaultImage={defaultShopImage}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20"></div>
            </div>

            {/* Shop Information Below Slider */}
            <div className="px-4 sm:px-6 lg:px-8 py-8">
                <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8">
                    <div className="space-y-6">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="w-3 h-3 bg-[#C37C00] rounded-full animate-pulse"></div>
                            <span className="text-[#C37C00] text-lg font-medium">Premium Gold Shop</span>
                        </div>

                        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                            {safeShop.name}
                        </h1>

                        <p className="text-lg text-gray-600 mb-2">
                            By {safeShop.ownerName || 'Fady'}
                        </p>

                        <p className="text-xl text-gray-700 mb-4 leading-relaxed">
                            {safeShop.description}
                        </p>

                        <div className="flex flex-wrap gap-6">
                            {safeShop.address && (
                                <div className="flex items-center gap-2">
                                    <MapPin className="w-5 h-5 text-[#C37C00]" />
                                    <span className="text-lg text-gray-700">{safeShop.address}</span>
                                </div>
                            )}

                            {safeShop.rating > 0 && (
                                <div className="flex items-center gap-2">
                                    <Star className="w-5 h-5 text-[#C37C00] fill-current" />
                                    <span className="text-lg font-semibold text-gray-700">{safeShop.rating.toFixed(1)}</span>
                                    <span className="text-gray-500">({safeReviews.length} reviews)</span>
                                </div>
                            )}
                        </div>

                        <div className="flex flex-wrap gap-4 mt-6">
                            <Button
                                onClick={() => navigate(ROUTES.BOOK_APPOINTMENT(id))}
                                className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-8 py-3 rounded-full font-bold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105"
                            >
                                <Calendar className="w-5 h-5 mr-2" />
                                Book Appointment
                            </Button>

                            {safeShop.whatsapp && (
                                <Button
                                    variant="outline"
                                    onClick={() => window.open(`https://wa.me/${safeShop.whatsapp}`, '_blank')}
                                    className="bg-white/20 hover:bg-white/30 text-[#C37C00] border-[#C37C00]/50 hover:border-[#C37C00] px-6 py-3 rounded-full font-semibold transition-all duration-300"
                                >
                                    <WhatsAppIcon />
                                    <span className="ml-2">WhatsApp</span>
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Shop Information Section - Below Slider */}
            <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-3xl shadow-2xl p-8 mb-8">
                    <div className="text-center">
                        {/* Shop Category Badge */}
                        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#C37C00] to-[#A66A00] text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg mb-4">
                            <span className="text-yellow-200">👑</span>
                            Premium Gold Shop
                        </div>

                        {/* Shop Name */}
                        <h1 className="text-5xl font-bold text-gray-900 mb-4 leading-tight">
                            {safeShop.name}
                        </h1>

                        {/* Shop Description */}
                        <p className="text-xl text-gray-600 mb-6 max-w-2xl mx-auto leading-relaxed">
                            {safeShop.description}
                        </p>

                        {/* Shop Owner */}
                        <div className="flex items-center justify-center gap-3 mb-6">
                            <div className="w-12 h-12 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center">
                                <span className="text-white font-bold text-lg">
                                    {safeShop.ownerName ? safeShop.ownerName.charAt(0).toUpperCase() : safeShop.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                            <div className="text-left">
                                <p className="text-lg font-semibold text-gray-900">
                                    Mustafa Lotfy Almanfaloty
                                </p>
                                <p className="text-sm text-gray-500">Verified Gold Merchant</p>
                            </div>
                        </div>

                        {/* Quick Info */}
                        <div className="flex flex-wrap justify-center gap-6 mb-8">
                            {safeShop.address && (
                                <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-full">
                                    <MapPin className="w-5 h-5 text-[#C37C00]" />
                                    <span className="text-gray-700 font-medium">{safeShop.address}</span>
                                </div>
                            )}

                            {safeShop.rating > 0 && (
                                <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-full">
                                    <Star className="w-5 h-5 text-[#C37C00] fill-current" />
                                    <span className="text-gray-700 font-bold">{safeShop.rating.toFixed(1)}</span>
                                    <span className="text-gray-500">({safeReviews.length} reviews)</span>
                                </div>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-wrap justify-center gap-4">
                            <Button
                                onClick={() => navigate(ROUTES.BOOK_APPOINTMENT(id))}
                                className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-8 py-4 rounded-full font-bold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105"
                            >
                                <Calendar className="w-5 h-5 mr-2" />
                                Book Appointment
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Products Section */}
            <div className="px-4 sm:px-6 lg:px-8 py-0">
                <div className="bg-white rounded-2xl shadow-xl p-8">
                    <div className="flex items-center justify-between mb-8">
                        <div className="flex items-center gap-4">
                            <h2 className="text-3xl font-bold text-gray-900">Our Products Collection</h2>
                            <Badge className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] text-white px-4 py-2 text-lg font-bold">
                                {safeProducts.length} Products
                            </Badge>
                        </div>

                        <div className="flex items-center gap-3">
                            <Button
                                variant={viewMode === 'grid' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('grid')}
                                className="rounded-full bg-[#C37C00] hover:bg-[#A66A00] text-white"
                            >
                                <Grid className="w-4 h-4" />
                            </Button>
                            <Button
                                variant={viewMode === 'list' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setViewMode('list')}
                                className="rounded-full bg-[#C37C00] hover:bg-[#A66A00] text-white"
                            >
                                <List className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>

                    {safeProducts.length > 0 ? (
                        <div className={viewMode === 'grid'
                            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
                            : "space-y-8"
                        }>
                            {safeProducts.map((product) => (
                                <div key={product.id || product._id} className="product-card">
                                    <Card className="group overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 bg-white rounded-3xl relative">
                                        {/* Premium Badge */}
                                        <div className="absolute top-4 left-4 z-10">
                                            <div className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                                PREMIUM GOLD
                                            </div>
                                        </div>
                                        <div className="relative overflow-hidden">
                                            <img
                                                src={`${import.meta.env.VITE_API_BASE_URL}/product-image/${product.logoUrl}`}
                                                alt={product.title || product.name}
                                                className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                                style={{
                                                    filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',
                                                }}
                                                onError={(e) => {
                                                    e.target.src = 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=300&fit=crop&crop=center&auto=format&q=60';
                                                }}
                                            />

                                            {/* Shimmer Effect */}
                                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                            <div className="absolute inset-0 bg-gradient-to-r from-[#C37C00]/10 via-transparent to-[#A66A00]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                            {/* Floating Action Buttons */}
                                            <div className="absolute top-4 right-4 flex flex-col gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-6 group-hover:translate-x-0">
                                                <Button
                                                    size="sm"
                                                    className="w-12 h-12 rounded-full bg-white/95 hover:bg-white text-gray-700 hover:text-red-500 shadow-xl backdrop-blur-md hover:scale-110 transition-all duration-300"
                                                    aria-label="Add to favorites"
                                                >
                                                    <Heart className="w-5 h-5" />
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    className="w-12 h-12 rounded-full bg-white/95 hover:bg-white text-gray-700 hover:text-[#C37C00] shadow-xl backdrop-blur-md hover:scale-110 transition-all duration-300"
                                                    onClick={() => navigate(ROUTES.PRODUCT_DETAILS(product.id || product._id))}
                                                    aria-label="View product details"
                                                >
                                                    <Eye className="w-5 h-5" />
                                                </Button>
                                            </div>

                                            {/* Price Badge */}
                                            <div className="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
                                                <div className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] text-white px-4 py-2 rounded-full font-bold text-sm shadow-xl backdrop-blur-md">
                                                    Contact for Price
                                                </div>
                                            </div>
                                        </div>

                                        <CardContent className="p-8">
                                            <div className="space-y-5">
                                                {/* Product Title */}
                                                <div className="space-y-2">
                                                    <h3 className="font-bold text-2xl text-gray-900 group-hover:text-[#C37C00] transition-colors duration-300 line-clamp-2 leading-tight">
                                                        {String(product.title || product.name || 'Untitled Product')}
                                                    </h3>
                                                    <div className="w-16 h-1 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full"></div>
                                                </div>

                                                {/* Product Description */}
                                                {product.description && (
                                                    <p className="text-gray-600 text-base line-clamp-3 leading-relaxed">
                                                        {String(product.description)}
                                                    </p>
                                                )}

                                                {/* Product Rating */}
                                                <div className="flex items-center justify-center py-3">
                                                    <div className="flex items-center gap-3 bg-gradient-to-r from-[#FFF8E6] to-[#F0E8DB] px-6 py-3 rounded-full border border-[#C37C00]/20 shadow-sm">
                                                        <div className="flex">
                                                            {[...Array(5)].map((_, i) => (
                                                                <Star
                                                                    key={i}
                                                                    className={`w-4 h-4 ${i < 4 ? 'fill-[#C37C00] text-[#C37C00]' : 'text-gray-300'}`}
                                                                />
                                                            ))}
                                                        </div>
                                                        <span className="text-sm font-bold text-[#C37C00]">4.8</span>
                                                        <span className="text-xs text-gray-500">(24 reviews)</span>
                                                    </div>
                                                </div>

                                                {/* Product Specifications */}
                                                <div className="flex flex-wrap justify-center gap-3">
                                                    {product.karat && (
                                                        <div className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                                            <span className="text-yellow-200">💎</span> {String(product.karat)}K Gold
                                                        </div>
                                                    )}
                                                    {product.weight && (
                                                        <div className="bg-gradient-to-r from-[#8A5700] to-[#6B4500] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                                            <span className="text-yellow-200">⚖️</span> {String(product.weight)}g
                                                        </div>
                                                    )}
                                                    {product.category && (
                                                        <div className="bg-gradient-to-r from-[#A66A00] to-[#8A5700] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                                            <span className="text-yellow-200">🏷️</span> {String(product.category)}
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Action Buttons */}
                                                <div className="flex gap-3 mt-6">
                                                    <Button
                                                        variant="outline"
                                                        className="flex-1 border-2 border-[#C37C00] text-[#C37C00] hover:bg-[#C37C00] hover:text-white font-semibold py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl"
                                                    >
                                                        <Heart className="w-4 h-4 mr-2" />
                                                        Add to Favorites
                                                    </Button>
                                                    <Button
                                                        className="flex-1 bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white font-bold py-3 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                                                        onClick={() => navigate(ROUTES.PRODUCT_DETAILS(product.id || product._id))}
                                                    >
                                                        <Eye className="w-4 h-4 mr-2" />
                                                        View Details
                                                    </Button>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-16 bg-gradient-to-br from-[#F8F4ED] to-[#F0E8DB] rounded-2xl border border-[#E2D2B6]/30">
                            <div className="w-24 h-24 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl">
                                <span className="text-4xl">📦</span>
                            </div>
                            <h3 className="text-2xl font-bold text-gray-900 mb-3">
                                No Products Available
                            </h3>
                            <p className="text-gray-600 mb-6 max-w-md mx-auto leading-relaxed">
                                {safeShop.name} hasn't added any products yet. Check back later for amazing jewelry collections!
                            </p>
                            {user?.role === 'admin' || user?.id === safeShop.ownerId ? (
                                <Button
                                    className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                                    人类的
                                    onClick={() => navigate(ROUTES.CREATE_PRODUCT)}
                                >
                                    Add Your First Product
                                </Button>
                            ) : (
                                <div className="bg-white/50 rounded-xl p-4 max-w-sm mx-auto">
                                    <p className="text-sm text-gray-600">
                                        Contact the shop owner to see their products or explore other shops
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ShopDetails;