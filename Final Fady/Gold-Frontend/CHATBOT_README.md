# المساعد الذكي - Floating Chat

## الوصف
تم تنفيذ زر عائم مع واجهة دردشة ذكية في صفحة الـ Home فقط. يتيح للمستخدمين التفاعل مع نظام الذكاء الاصطناعي للحصول على معلومات حول المنتجات والمتاجر.

## الملفات المضافة

### Frontend Components:
1. `src/components/ui/FloatingChatButton.jsx` - الزر العائم
2. `src/components/ui/ChatInterface.jsx` - واجهة الدردشة
3. `src/components/ui/FloatingChat.jsx` - المكون الرئيسي الذي يجمع الاثنين
4. `src/services/chatbotService.js` - خدمة الاتصال بـ API الباك إند

### التحديثات:
1. `src/utils/constants.js` - إضافة endpoint الـ chatbot
2. `src/pages/Home.jsx` - إضافة المكون إلى صفحة الـ Home
3. `Gold-Backend/index.js` - تحديث CORS للسماح بالاتصال من الفرونت إند

## الميزات

### 1. الزر العائم
- يظهر في الزاوية اليمنى السفلى من صفحة الـ Home
- يتغير لونه وأيقونته حسب حالة الدردشة (مفتوح/مغلق)
- تأثيرات بصرية جميلة مع hover وانيميشن

### 2. واجهة الدردشة
- تصميم عصري وجذاب باللون الذهبي
- دعم كامل للغة العربية (RTL)
- رسائل منفصلة للمستخدم والبوت
- عرض الوقت لكل رسالة
- مؤشر "يكتب..." أثناء انتظار الرد
- scroll تلقائي للرسائل الجديدة

### 3. إدارة المصادقة
- التحقق من تسجيل دخول المستخدم
- عرض رسالة ترحيب شخصية للمستخدمين المسجلين
- زر تسجيل دخول للمستخدمين غير المسجلين
- منع إرسال الرسائل بدون تسجيل دخول

### 4. معالجة الأخطاء
- رسائل خطأ واضحة ومفيدة
- إعادة المحاولة التلقائية
- حالات تحميل مرئية

## كيفية الاستخدام

### للمستخدمين المسجلين:
1. انقر على الزر العائم في صفحة الـ Home
2. اكتب سؤالك في حقل النص
3. انقر على زر الإرسال أو اضغط Enter
4. انتظر رد المساعد الذكي

### للمستخدمين غير المسجلين:
1. انقر على الزر العائم
2. انقر على زر "تسجيل الدخول"
3. أكمل عملية تسجيل الدخول
4. عد إلى صفحة الـ Home واستخدم المساعد

## أمثلة على الأسئلة
- "ما هي المنتجات المتاحة؟"
- "كم عدد المتاجر في المنصة؟"
- "أريد معلومات عن الخواتم الذهبية"
- "ما هي أنواع الذهب المتاحة؟"

## المتطلبات التقنية

### Backend:
- يجب أن يعمل الباك إند على البورت 5001
- مفتاح OpenAI API صحيح في متغيرات البيئة
- قاعدة بيانات MongoDB متصلة

### Frontend:
- React 19+
- Tailwind CSS
- Lucide React للأيقونات
- Axios للـ HTTP requests

## الاختبار
يمكنك استخدام مكون `ChatTest.jsx` لاختبار الوظائف:

```jsx
import ChatTest from './src/components/ui/ChatTest.jsx';
// استخدم هذا المكون في route منفصل للاختبار
```

## ملاحظات مهمة
- المساعد يظهر فقط في صفحة الـ Home
- يتطلب تسجيل دخول للاستخدام
- يستخدم نفس APIs الموجودة في الباك إند بدون تعديل
- التصميم متجاوب ويعمل على جميع الأحجام
