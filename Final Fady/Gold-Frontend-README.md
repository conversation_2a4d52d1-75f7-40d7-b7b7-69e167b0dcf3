# 🏆 Gold Market Platform - Complete Project Documentation

## 📋 Table of Contents
- [Project Overview](#project-overview)
- [Features](#features)
- [Technology Stack](#technology-stack)
- [Installation & Setup](#installation--setup)
- [Project Structure](#project-structure)
- [API Documentation](#api-documentation)
- [User Roles & Permissions](#user-roles--permissions)
- [Screenshots](#screenshots)
- [Contributing](#contributing)
- [License](#license)

## 🌟 Project Overview

**Gold Market Platform** is a comprehensive digital marketplace designed specifically for the Egyptian gold and jewelry industry. This full-stack web application connects customers with verified gold shops, enabling seamless browsing, appointment booking, and secure transactions.

### Vision Statement
*"Revolutionizing the traditional gold market through digital innovation, bringing transparency, convenience, and trust to jewelry shopping in Egypt."*

### Key Highlights
- 🏪 **Multi-Shop Platform**: Supports unlimited verified gold shops
- 👥 **Dual User System**: Customers and Shop Owners with Admin oversight
- 🤖 **AI-Powered**: Intelligent chatbot using OpenAI GPT-4
- 📱 **Mobile-First**: Fully responsive design for all devices
- 🔒 **Secure**: JWT authentication with Google OAuth integration

## 🚀 Features

### 👤 User Management
- **Multi-Role Authentication**: Customer, Shop Owner, Admin roles
- **Google OAuth Integration**: Quick social login
- **Profile Management**: Complete user profiles with preferences
- **Favorites System**: Save preferred products and shops
- **Review System**: Rate and review shops with detailed feedback

### 🏪 Shop Management
- **Complete Business Profiles**: Name, description, location, contact information
- **Image Galleries**: Multiple high-quality shop photos with slider
- **Product Catalog**: Rich product data with specifications
- **Verification System**: Admin-approved shop verification
- **Analytics Dashboard**: Performance metrics and insights

### 📅 Smart Booking System
- **Real-Time Availability**: Live calendar integration
- **Flexible Scheduling**: Customizable appointment durations
- **Instant Confirmations**: Email/SMS notifications
- **Cancellation Management**: Easy rescheduling options
- **Appointment Types**: Consultation, purchase, custom design

### ⭐ Rating & Review System
- **5-Star Rating**: Numerical scoring system
- **Written Reviews**: Detailed customer feedback
- **Photo Reviews**: Visual testimonials
- **Response System**: Shop owner replies to reviews
- **Trust Building**: Verified reviews from actual customers

### 🤖 AI Integration
- **Intelligent Chatbot**: Natural language processing with context awareness
- **Auto Content Generation**: AI-powered product descriptions
- **24/7 Support**: Always available customer assistance
- **Bilingual Support**: Arabic and English language support

## 💻 Technology Stack

### Frontend
- **React 18**: Latest React with concurrent features
- **Vite**: Lightning-fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: High-quality, accessible component library
- **Lucide React**: Beautiful icon library
- **React Router DOM**: Client-side routing
- **Axios**: HTTP client for API communication

### Backend
- **Node.js**: JavaScript runtime environment
- **Express.js**: Web application framework
- **MongoDB**: NoSQL database for flexible data storage
- **Mongoose**: MongoDB object modeling for Node.js
- **JWT**: JSON Web Tokens for secure authentication
- **bcrypt**: Password hashing for security

### AI & External Services
- **OpenAI GPT-4**: Advanced AI for chatbot and content generation
- **Google OAuth 2.0**: Social authentication
- **Multer**: File upload handling
- **Nodemailer**: Email service integration
- **Socket.IO**: Real-time communication

### Development Tools
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Git**: Version control
- **npm/pnpm**: Package management

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local or cloud instance)
- Git

### Frontend Setup
```bash
# Clone the repository
git clone <repository-url>
cd Gold-Frontend

# Install dependencies
npm install
# or
pnpm install

# Create environment file
cp .env.example .env

# Configure environment variables
VITE_API_BASE_URL=http://localhost:5002
VITE_GOOGLE_CLIENT_ID=your_google_client_id

# Start development server
npm run dev
# or
pnpm dev
```

### Backend Setup
```bash
# Navigate to backend directory
cd Gold-Backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Configure environment variables
PORT=5002
MONGODB_URI=mongodb://localhost:27017/gold-market
JWT_SECRET=your_jwt_secret
OPENAI_API_KEY=your_openai_api_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Start development server
npm run dev
```

### Environment Variables

#### Frontend (.env)
```env
VITE_API_BASE_URL=http://localhost:5002
VITE_GOOGLE_CLIENT_ID=your_google_client_id
```

#### Backend (.env)
```env
PORT=5002
MONGODB_URI=mongodb://localhost:27017/gold-market
JWT_SECRET=your_super_secret_jwt_key
OPENAI_API_KEY=sk-your_openai_api_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

## 📁 Project Structure

### Frontend Structure
```
Gold-Frontend/
├── public/
│   ├── logo.webp
│   └── index.html
├── src/
│   ├── components/
│   │   ├── ui/           # shadcn/ui components
│   │   ├── layout/       # Layout components
│   │   └── common/       # Reusable components
│   ├── pages/
│   │   ├── auth/         # Authentication pages
│   │   ├── shop/         # Shop-related pages
│   │   ├── product/      # Product pages
│   │   └── dashboard/    # Dashboard pages
│   ├── services/
│   │   ├── api.js        # API configuration
│   │   ├── authService.js
│   │   ├── shopService.js
│   │   └── productService.js
│   ├── context/
│   │   └── AuthContext.jsx
│   ├── utils/
│   │   └── constants.js
│   └── App.jsx
├── package.json
└── vite.config.js
```

### Backend Structure
```
Gold-Backend/
├── controllers/
│   ├── authController.js
│   ├── shopController.js
│   ├── productController.js
│   └── chatbotController.js
├── models/
│   ├── User.js
│   ├── Shop.js
│   ├── Product.js
│   └── BookingTime.js
├── middleware/
│   ├── auth.js
│   ├── validation.js
│   └── upload.js
├── routes/
│   ├── auth.js
│   ├── shops.js
│   ├── products.js
│   └── chatbot.js
├── services/
│   ├── openaiService.js
│   └── emailService.js
├── uploads/
│   ├── shops/
│   └── products/
├── app.js
└── server.js
```

## 🔐 User Roles & Permissions

### Customer Role
- Browse shops and products
- Create and manage favorites
- Book appointments with shops
- Rate and review shops
- Chat with AI assistant
- Manage personal profile

### Shop Owner Role
- Create and manage shop profile
- Add, edit, delete products
- Manage appointment availability
- Upload shop and product images
- Respond to customer reviews
- View analytics and insights

### Admin Role
- Approve/reject shop applications
- Manage all users and shops
- Monitor platform activity
- Access system analytics
- Moderate content and reviews
- Manage platform settings

## 📱 Key Pages & Features

### Homepage
- Dynamic shop slider with golden theme
- Search functionality for shops and products
- Category navigation for jewelry types
- Featured shops and products
- AI chatbot integration

### Shop Details Page
- Full-width image slider without gaps
- Comprehensive business information
- Product showcase with enhanced cards
- Real-time booking interface
- Customer reviews and ratings

### Product Catalog
- Grid and list view options
- Advanced filtering and sorting
- Detailed product specifications
- High-quality image galleries
- AI-generated descriptions

### User Dashboard
- Profile management
- Booking history and management
- Favorites collection
- Review management
- Account settings

### Admin Dashboard
- Shop verification management
- User management
- Platform analytics
- Content moderation
- System settings

## 🤖 AI Features

### Intelligent Chatbot
```javascript
// Example chatbot implementation
const getChatbotResponse = async (message, user) => {
  const keywords = message.split(/\s+/).filter(Boolean);
  
  const products = await Product.find({
    $or: [
      { title: { $regex: keywords, $options: "i" } },
      { description: { $regex: keywords, $options: "i" } }
    ]
  }).populate('shop', 'name');
  
  const context = products.map(p => 
    `${p.title} - ${p.description} (Shop: ${p.shop.name})`
  ).join('; ');
  
  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "You are a helpful assistant for a gold platform." },
      { role: "user", content: `Context: ${context}\nUser: ${message}` }
    ],
    max_tokens: 300,
    temperature: 0.2
  });
  
  return response.choices[0].message.content;
};
```

### Auto Content Generation
- Product description generation
- SEO-optimized content
- Multi-language support
- Variation generation

## 🔗 API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/google` - Google OAuth login
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update user profile

### Shops
- `GET /shop` - Get all shops (authenticated)
- `GET /shop/public` - Get public shops
- `GET /shop/:id` - Get shop by ID
- `POST /shop` - Create new shop
- `PUT /shop/:id` - Update shop
- `DELETE /shop/:id` - Delete shop

### Products
- `GET /product` - Get all products
- `GET /product/shop/:shopId` - Get products by shop
- `GET /product/:id` - Get product by ID
- `POST /product` - Create new product
- `PUT /product/:id` - Update product
- `DELETE /product/:id` - Delete product

### Booking
- `GET /available/:shopId` - Get available times
- `POST /available-time` - Create available time
- `POST /book` - Book appointment
- `GET /my-bookings` - Get user bookings
- `DELETE /cancel/:timeId` - Cancel booking

### AI Chatbot
- `POST /chatbot` - Send message to chatbot
- `GET /chatbot/history` - Get chat history

## 🎨 Design System

### Color Palette
- **Primary Gold**: #C37C00
- **Secondary Gold**: #A66A00
- **Dark Gold**: #8A5700
- **Background**: #F8F4ED to #F0E8DB (gradient)
- **Text**: #1F2937 (dark gray)
- **Accent**: #FFF8E6 (light gold)

### Typography
- **Font Family**: Inter (Google Fonts)
- **Headings**: 700-800 weight
- **Body**: 400-500 weight
- **Captions**: 300 weight

### Components
- **Buttons**: Rounded corners, gradient backgrounds
- **Cards**: Subtle shadows, hover effects
- **Forms**: Clean inputs with validation
- **Navigation**: Fixed header with golden theme

## 🚀 Deployment

### Frontend Deployment
```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

### Backend Deployment
```bash
# Set NODE_ENV to production
export NODE_ENV=production

# Start production server
npm start
```

### Environment Setup
- Configure production MongoDB URI
- Set secure JWT secret
- Configure email service
- Set up file upload storage
- Configure CORS for production domain

## 🔮 Future Roadmap

### Phase 1: Enhanced Features (Q2 2024)
- AI-powered product recommendations
- Visual search functionality
- Payment gateway integration
- Advanced filtering options

### Phase 2: Mobile Applications (Q3 2024)
- Native iOS application
- Native Android application
- Push notifications
- Offline functionality

### Phase 3: Advanced Analytics (Q4 2024)
- Business intelligence dashboard
- Predictive analytics
- Voice assistant integration
- Image recognition

### Phase 4: Global Expansion (Q1 2025)
- Multi-vendor marketplace
- International markets
- Multi-language support
- Global shipping integration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow ESLint and Prettier configurations
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed
- Follow the existing code style

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact & Support

- **Project Repository**: [GitHub Repository]
- **Live Demo**: http://localhost:5173
- **API Documentation**: http://localhost:5002/api-docs
- **Issues**: [GitHub Issues]

---

*Built with ❤️ for the Egyptian gold industry. Combining traditional craftsmanship with modern technology.*
